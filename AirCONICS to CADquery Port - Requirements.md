AirCONICS to CADquery Port - Requirements Document
(Updated Version)
1. Project Overview
1.1 Purpose
This document defines the requirements for porting AirCONICS (Aircraft CONfiguration through Integrated Cross-disciplinary Scripting) from its current Rhinoceros plugin implementation to CADquery, a Python-based parametric CAD scripting framework.
1.2 Background
•	Source System: AirCONICS is currently implemented as a Rhino plugin using rhinoscriptsyntax and OpenNURBS
•	Target System: CADquery is a Python library built on OpenCASCADE Technology (OCCT)
•	Goal: Create a standalone Python package that maintains AirCONICS functionality while leveraging CADquery's parametric modeling capabilities
1.3 Scope
The port will include all core AirCONICS functionality for aircraft geometry generation, parametric modeling, and cross-disciplinary integration capabilities.
2. System Architecture Requirements
2.1 Technology Stack
•	Primary Language: Python 3.8+ with explicit CI/CD testing for versions 3.8 through 3.12
•	CAD Kernel: CADquery 2.3+ (based on OpenCASCADE Technology)
•	Dependencies: NumPy, SciPy, matplotlib for mathematical computations
•	Optional Dependencies: jupyter for interactive development, pytest for testing
•	Containerization: Docker support for development and deployment environments
•	Policy for adopting new Python versions as they become stable
•	Dependency management strategy for handling future CADquery updates
2.2 Package Structure
airconics_cadquery/
├── __init__.py
├── core/
│   ├── __init__.py
│   ├── geometry.py          # Core geometry classes
│   ├── curves.py            # Curve generation utilities
│   └── surfaces.py          # Surface generation utilities
├── components/
│   ├── __init__.py
│   ├── wing.py              # Wing geometry classes
│   ├── fuselage.py          # Fuselage geometry classes
│   ├── empennage.py         # Tail components
│   └── propulsion.py        # Engine/propulsion components
├── primitives/
│   ├── __init__.py
│   ├── airfoils.py          # Airfoil generation and manipulation
│   ├── lofting.py           # Lofting operations
│   └── blending.py          # Surface blending utilities
├── utilities/
│   ├── __init__.py
│   ├── coordinate_systems.py # Coordinate transformations
│   ├── materials.py         # Material properties
│   └── export.py            # Export utilities
├── examples/
│   ├── basic_wing.py
│   ├── complete_aircraft.py
│   └── jetstream31_example.py
└── tests/
    ├── test_geometry.py
    ├── test_components.py
    └── test_primitives.py
3. Functional Requirements
3.1 Core Geometry Engine
•	Requirement ID: F001
•	Description: Implement fundamental geometric operations using CADquery
•	Priority: High
•	Acceptance Criteria: 
o	Create and manipulate 3D curves (NURBS, Bezier, polylines)
o	Generate and modify surfaces (ruled, lofted, swept)
o	Perform boolean operations (union, intersection, difference)
o	Support coordinate system transformations
3.2 Airfoil Generation
•	Requirement ID: F002
•	Description: Generate parametric airfoil geometries
•	Priority: High
•	Acceptance Criteria: 
o	Support NACA 4-digit, 5-digit, and 6-series airfoils
o	Import airfoil coordinates from standard formats (DAT files)
o	Generate airfoil surfaces with thickness distribution
o	Support airfoil blending and morphing
3.3 Wing Geometry
•	Requirement ID: F003
•	Description: Create parametric wing geometries
•	Priority: High
•	Acceptance Criteria: 
o	Generate wings with specified planform parameters (span, chord, sweep, twist)
o	Support multi-section wings with different airfoils
o	Implement wing-body blending
o	Generate control surfaces (ailerons, flaps, slats)
3.4 Fuselage Geometry
•	Requirement ID: F004
•	Description: Create parametric fuselage geometries
•	Priority: High
•	Acceptance Criteria: 
o	Generate fuselage from cross-sectional definitions
o	Support nose and tail cone generation
o	Implement fuselage-wing integration
o	Handle cabin and cargo compartment definitions
3.5 Empennage Components
•	Requirement ID: F005
•	Description: Generate tail components (vertical and horizontal stabilizers)
•	Priority: Medium
•	Acceptance Criteria: 
o	Create vertical and horizontal stabilizers
o	Support T-tail and conventional configurations
o	Generate rudder and elevator surfaces
o	Implement stabilizer-fuselage integration
3.6 Propulsion Integration
•	Requirement ID: F006
•	Description: Integrate propulsion system geometries
•	Priority: Medium
•	Acceptance Criteria: 
o	Generate engine nacelles and pylons
o	Support propeller geometries
o	Implement engine-wing/fuselage integration
o	Handle intake and exhaust geometries
3.7 Lofting Operations
•	Requirement ID: F007
•	Description: Implement advanced lofting capabilities
•	Priority: High
•	Acceptance Criteria: 
o	Loft between multiple airfoil sections
o	Support ruled and smooth lofting
o	Handle twist and scaling along loft path
o	Generate high-quality surface continuity
3.8 Export Capabilities
•	Requirement ID: F008
•	Description: Export geometries to standard formats
•	Priority: Medium
•	Acceptance Criteria: 
o	Export to STEP format for CAD integration
o	Generate STL files for 3D printing
o	Support mesh export for CFD analysis
o	Export to neutral formats (IGES, BREP)

3.9 Analysis Tool Integration
•	Requirement ID: F009
•	Description: Integration with external analysis tools
•	Priority: Medium
•	Acceptance Criteria: 
o	Support for mesh export to popular CFD tools (OpenFOAM, Fluent, etc.)
o	Integration with structural analysis packages
o	API hooks for automated analysis workflows
o	Data exchange formats and standards compliance

3.10 Parametric Assembly System
•	Requirement ID: F010
•	Description: Create a robust parameter management and assembly system
•	Priority: High
•	Acceptance Criteria: 
o	Centralized parameter repository with change propagation
o	Support for parameter expressions and formulas
o	Hierarchical parameter inheritance between components
o	Parameter validation with constraints and bounds checking
o	Parameter history tracking and state management

3.11 Assembly Constraint System
•	Requirement ID: F011
•	Description: Implement a constraint-based assembly system
•	Priority: High
•	Acceptance Criteria: 
o	Geometric constraints (mate, align, tangent, etc.)
o	Kinematic constraints for movable assemblies
o	Automatic validation of assembly integrity after parameter changes
o	Conflict resolution for overconstrained systems
o	Visual feedback for constraint violations
4. Non-Functional Requirements
4.1 Performance
•	Requirement ID: NF001
•	Description: System performance standards
•	Acceptance Criteria: 
o	Generate basic wing geometry in <5 seconds (stretch goal: <2 seconds)
o	Support models with 10,000+ surface elements
o	Memory usage <2GB for typical aircraft models
o	Efficient handling of parametric updates
o	Parameter change propagation in <1 second for assemblies with <100 parts
o	Assembly reconfiguration in <3 seconds for constraint updates
4.2 Usability
•	Requirement ID: NF002
•	Description: User interface and experience requirements
•	Acceptance Criteria: 
o	Intuitive Python API similar to original AirCONICS
o	Comprehensive documentation with examples
o	Jupyter notebook compatibility
o	Clear error messages and debugging information
4.3 Maintainability
•	Requirement ID: NF003
•	Description: Code quality and maintenance standards
•	Acceptance Criteria: 
o	Python PEP 8 compliance
o	80% test coverage
o	Modular design with clear separation of concerns
o	Comprehensive inline documentation
4.4 Compatibility
•	Requirement ID: NF004
•	Description: System compatibility requirements
•	Acceptance Criteria: 
o	Support Python 3.8, 3.9, 3.10, 3.11, 3.12
o	Cross-platform compatibility (Windows, macOS, Linux)
o	CADquery 2.3+ compatibility
o	Backward compatibility with existing AirCONICS scripts (where possible)

4.5 Error Handling
•	Requirement ID: NF005
•	Description: Robust error handling and fault tolerance
•	Acceptance Criteria: 
o	Error classification framework with severity levels
o	Comprehensive exception handling
o	Detailed logging with configurable verbosity
o	Recovery mechanisms for long-running operations
o	User-friendly error messages with remediation suggestions

4.6 Versioning and Backward Compatibility
•	Requirement ID: NF006
•	Description: Clear versioning strategy and compatibility guarantees
•	Acceptance Criteria: 
o	Semantic versioning policy (MAJOR.MINOR.PATCH)
o	API stability guarantees between major versions
o	Migration tools for users of previous versions
o	Deprecation policy with advance notice periods
o	Documentation of breaking changes between versions
5. Technical Implementation Details
5.1 Geometry Abstraction Layer
Create an abstraction layer that maps AirCONICS geometric operations to CADquery equivalents:
# Example mapping
rhinoscriptsyntax.AddLoftSrf() → cadquery.Workplane().loft()
rhinoscriptsyntax.AddNurbsCurve() → cadquery.Workplane().spline()
rhinoscriptsyntax.BooleanUnion() → cadquery.Workplane().union()
5.2 Coordinate System Handling
•	Implement coordinate system transformation utilities
•	Handle differences between Rhino and CADquery coordinate conventions
•	Support aircraft-specific coordinate systems (body, stability, wind axes)
5.3 Parametric Design Patterns
•	Use CADquery's parametric capabilities for design optimization
•	Implement parameter validation and constraints
•	Support design variable linking and dependencies
•	Create parameter proxy objects for change tracking
•	Implement observer pattern for parameter updates

5.5 Component Interface Management
•	Define standardized interface points between components
•	Implement mate connectors and attachment points
•	Create explicit interface contracts between components
•	Support for parametric interfaces that scale appropriately
•	Validation system for interface compatibility
•	Automatic interface adapters for component connections
5.4 Mathematical Libraries Integration
•	Integrate NumPy for array operations and linear algebra
•	Use SciPy for optimization and numerical methods
•	Support matplotlib for visualization and plotting
6. Data Migration Requirements
6.1 Configuration Files
•	Requirement ID: DM001
•	Description: Migrate existing AirCONICS configuration files
•	Acceptance Criteria: 
o	Parse existing aircraft configuration files
o	Convert parameter formats to CADquery equivalents
o	Maintain backward compatibility where possible
6.2 Airfoil Database
•	Requirement ID: DM002
•	Description: Port airfoil database and utilities
•	Acceptance Criteria: 
o	Import existing airfoil coordinate files
o	Maintain airfoil database structure
o	Support airfoil interpolation and blending
6.3 Material Properties
•	Requirement ID: DM003
•	Description: Migrate material property definitions
•	Acceptance Criteria: 
o	Convert material property files
o	Support material assignment to geometric components
o	Maintain material database integrity
7. Testing Requirements
7.1 Unit Testing
•	Requirement ID: T001
•	Description: Comprehensive unit test coverage
•	Acceptance Criteria: 
o	Test all geometric operations
o	Validate parametric updates
o	Test error handling and edge cases
o	80% code coverage
7.2 Integration Testing
•	Requirement ID: T002
•	Description: Component integration validation
•	Acceptance Criteria: 
o	Test wing-fuselage integration
o	Validate complete aircraft assembly
o	Test export functionality
o	Performance benchmarking
7.3 Regression Testing
•	Requirement ID: T003
•	Description: Ensure consistent behavior across versions
•	Acceptance Criteria: 
o	Compare outputs with original AirCONICS
o	Geometric accuracy validation
o	Performance regression detection
o	API compatibility testing
8. Documentation Requirements
8.1 API Documentation
•	Requirement ID: D001
•	Description: Comprehensive API documentation
•	Deliverables: 
o	Sphinx-generated documentation
o	Class and method docstrings
o	Type hints throughout codebase
o	API reference guide
8.2 User Guide
•	Requirement ID: D002
•	Description: User-friendly documentation
•	Deliverables: 
o	Getting started tutorial
o	Example gallery
o	Migration guide from Rhino AirCONICS
o	Best practices guide
8.3 Developer Documentation
•	Requirement ID: D003
•	Description: Technical documentation for contributors
•	Deliverables: 
o	Architecture overview
o	Contribution guidelines
o	Code style guide
o	Testing procedures
9. Deployment and Distribution
9.1 Package Distribution
•	Requirement ID: DEP001
•	Description: Python package distribution
•	Acceptance Criteria: 
o	PyPI package distribution
o	Conda package availability
o	Docker container support
o	Installation documentation
9.2 Continuous Integration
•	Requirement ID: DEP002
•	Description: Automated testing and deployment
•	Acceptance Criteria: 
o	GitHub Actions CI/CD pipeline
o	Automated testing on multiple platforms
o	Automated documentation generation
o	Release automation
10. Success Criteria
10.1 Primary Success Metrics
•	Complete geometric functionality equivalent to original AirCONICS
•	Performance within 20% of original implementation
•	Successful migration of all example aircraft models
•	Positive user feedback from alpha/beta testing
10.2 Secondary Success Metrics
•	Improved parametric modeling capabilities
•	Enhanced visualization features
•	Better integration with Python ecosystem
•	Expanded export format support
11. Project Timeline and Milestones
Phase 1: Foundation (Weeks 1-4)
•	Set up project structure and development environment
•	Implement core geometry abstraction layer
•	Create basic coordinate system utilities
•	Establish testing framework
Phase 2: Core Components (Weeks 5-12)
•	Implement airfoil generation and manipulation
•	Develop wing geometry classes
•	Create fuselage generation capabilities
•	Implement lofting operations
Phase 3: Advanced Features (Weeks 13-20)
•	Add empennage components
•	Implement propulsion integration
•	Develop surface blending utilities
•	Create export functionality
Phase 4: Integration and Testing (Weeks 21-24)
•	Complete system integration
•	Comprehensive testing and validation
•	Performance optimization
•	Documentation completion
12. Risk Assessment
12.1 Technical Risks
•	Geometric Accuracy: Differences between Rhino and CADquery geometric kernels
•	Performance: Potential performance degradation during conversion
•	Compatibility: API differences between rhinoscriptsyntax and CADquery
•	Assembly Integrity: Ensuring valid assemblies under parameter changes
•	Constraint Solving: Managing complex constraint systems efficiently
•	OpenCASCADE Limitations: Identifying and working around geometric edge cases
•	CADquery API Evolution: Handling future changes to the CADquery API
12.2 Mitigation Strategies
•	Implement comprehensive validation testing
•	Create performance benchmarks and optimization targets
•	Develop abstraction layers to minimize API differences
•	Maintain close collaboration with CADquery community
•	Create detailed testing procedures for constraint systems
•	Implement fallback mechanisms for geometric edge cases
•	Develop contingency plans for CADquery API changes
•	Create strategy for handling geometric operations differently between platforms
13. Conclusion
This requirements document provides a comprehensive framework for porting AirCONICS from Rhino to CADquery. The implementation should maintain the core functionality while leveraging CADquery's advantages in parametric modeling and Python ecosystem integration. Success depends on careful attention to geometric accuracy, performance optimization, and maintaining the intuitive API that makes AirCONICS valuable for aircraft design applications.

